import {Context, Hono} from "hono";
import {routeRequest} from "./service/router";
import {handleRobotsRequest} from "./service/robots";
import {filterDevEnv} from "./service/filters";

import jumio from "./service/router/jumio";
import pragmatic from "./service/router/gaming/pragmatic";
import prizeout from "./service/router/payment/prizeout";
import nuvei from "./service/router/payment/nuvei";
import skrill from "./service/router/payment/skrill";
import trustly from "./service/router/payment/trustly";
import masspay from "./service/router/payment/masspay";
import worldpay from "./service/router/payment/worldpay";
import emerchantpay from "./service/router/payment/emerchantpay";
import fiserv from "./service/router/payment/fiserv";
import rapyd from "./service/router/payment/rapyd";
import omni from "./service/router/gaming/omni";
import airwallex from "./service/router/payment/airwallex";
import checkout from "./service/router/payment/checkout";
import paynearme from "./service/router/payment/paynearme";
import veriff from "./service/router/fraud/veriff";

const gateway: Hono = new Hono();
const routable: Hono = new Hono();
const excludedOmniPaths = ["/v1/omni/freespins"];

const handleOmniRequest = async (ctx, next) => {
    const path = ctx.req.path;
    const isExcluded = excludedOmniPaths.some(excluded => path.includes(excluded));

    if (isExcluded) {
        return next();
    }

    return routeRequest(ctx, omni);
};

routable.use('*', filterDevEnv);

// general
routable.post('/v1/hook/jumio-docs/.*', (ctx: Context) => routeRequest(ctx, jumio))

// gaming
routable.post('/v1/pragmatic.*', (ctx: Context) => routeRequest(ctx, pragmatic))
routable.post('/v1/omni.*', handleOmniRequest)

// payments
routable.post('/v1/prizeout/callback/.*', (ctx: Context) => routeRequest(ctx, prizeout))
routable.post('/v1/hook/nuvei/mazooma/.*', (ctx: Context) => routeRequest(ctx, nuvei))
routable.post('/v1/hook/.*nuvei_mazooma_ach/.*', (ctx: Context) => routeRequest(ctx, nuvei))
routable.post('/v1/hook/skrill/.*', (ctx: Context) => routeRequest(ctx, skrill))
routable.post('/v1/hook/trustly/.*', (ctx: Context) => routeRequest(ctx, trustly))
routable.post('/v1/hook/masspay/.*', (ctx: Context) => routeRequest(ctx, masspay))
routable.post('/v1/hook/worldpay.*', (ctx: Context) => routeRequest(ctx, worldpay))
routable.post('/v1/hook/emerchantpay.*', (ctx: Context) => routeRequest(ctx, emerchantpay))
routable.post('/v1/hook/fiserv.*', (ctx: Context) => routeRequest(ctx, fiserv))
routable.post('/v1/hook/rapyd.*', (ctx: Context) => routeRequest(ctx, rapyd))
routable.post('/v1/hook/airwallex/.*', (ctx: Context) => routeRequest(ctx, airwallex))
routable.post('/v1/hook/checkout.*', (ctx: Context) => routeRequest(ctx, checkout))
routable.post('/v1/hook/paynearme/.*', (ctx: Context) => routeRequest(ctx, paynearme))

// fraud
routable.post('/v1/hook/veriff.*', (ctx: Context) => routeRequest(ctx, veriff))
routable.get('/robots.txt', (ctx: Context) => handleRobotsRequest(ctx));

gateway.route('/', routable);

export default gateway;