import {fetchOrigin, handleCfWorkerHeader} from "./service/filters";

import {Hono} from "hono";
import {logger} from "hono/logger";
import frontend from "./frontend";
import gateway from "./gateway";
import aggregator from "./aggregator";
import {FRONTEND, GATEWAY, AGGREGATOR, getServiceRoute} from "./util/utils";

const app: Hono = new Hono<{ Bindings: CloudflareEnv }>({
    getPath: (req: Request) => {
        const url: URL = new URL(req.url);

        return getServiceRoute(url.hostname) + url.pathname;
    }
});

// filters
app.use("*", logger());
app.use("*", handleCfWorkerHeader);

app.notFound(fetchOrigin);

// handlers
app.route(FRONTEND.getPath(), frontend);
app.route(GATEWAY.getPath(), gateway);
app.route(AGGREGATOR.getPath(), aggregator);

export default app;