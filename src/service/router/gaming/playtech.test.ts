import {RoutingService} from "../../router.types";
import playtech, {USER_NAME_PARAM} from "./playtech";

describe('Playtech Routing Service', () => {
    let service: RoutingService = playtech;

    const salt: string = 'xxxxxxx';
    const userId: string = '123';
    const currency: string = 'SC';
    const brandNameSuffix: string = 'sr';

    describe('resolveRoutingKey', () => {
        it('should resolve brand suffix (as a routing key) from player id', async () => {
            const username = `${salt}_${userId}_${currency}_${brandNameSuffix}`;
            const mockRequest = toMockRequest({ [USER_NAME_PARAM]: username });

            const result = await service.resolveRoutingKey(mockRequest, {} as any);

            expect(result).toEqual(brandNameSuffix);
        });
    });

    function toMockRequest(body: Record<string, string>): Request {
        return new Request('https://example.com', {
            method: 'POST',
            body: JSON.stringify(body),
            headers: {
                'Content-Type': 'application/json',
            },
        });
    }
});
