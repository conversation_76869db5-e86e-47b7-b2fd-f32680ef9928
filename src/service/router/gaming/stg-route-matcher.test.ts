import {RoutingService} from "../../router.types";
import stg, {STG, DEV_ENV_NAME, STG_ENV_NAME} from "./stg-route-matcher";

describe('Stg Routing Service', () => {
    let service: RoutingService = stg;

    const salt: string = 'xxxxxxx';
    const userId: string = '123';
    const currency: string = 'SC';
   

    describe('resolveRoutingKeyAsStage', () => {
        it('should resolve env suffix (as a routing key) from player id and return stage', async () => {
            const username = `${salt}_${userId}${STG}_${currency}`;
            const mockRequest = toMockPostRequest({ ['username']: username });

            const result = await service.resolveRoutingKey(mockRequest, {} as any);

            expect(result).toEqual(STG_ENV_NAME);
        });
    });

    describe('resolveRoutingKeyAsStageFromUpperCase', () => {
        it('should resolve env suffix in UpperCase (as a routing key) from player id and return stage', async () => {
            const username = `${salt}_${userId}${STG.toUpperCase()}_${currency}`;
            const mockRequest = toMockPostRequest({ ['username']: username });

            const result = await service.resolveRoutingKey(mockRequest, {} as any);

            expect(result).toEqual(STG_ENV_NAME);
        });
    });

    describe('resolveRoutingKeyAsStageFromURLIfGetRequest', () => {
        it('should resolve env suffix in UpperCase (as a routing key) from player id and return stage', async () => {
            const mockRequest = toMockGetRequest(`username&${STG}`);

            const result = await service.resolveRoutingKey(mockRequest, {} as any);

            expect(result).toEqual(STG_ENV_NAME);
        });
    });

    describe('resolveRoutingKeyAsStageFromXML', () => {
        it('should resolve env suffix in XML (as a routing key) from player id and return stage', async () => {
            const username = `<${STG.toUpperCase()}_${currency}/>`;
            const mockRequest = toMockPostRequest({ ['username']: username });

            const result = await service.resolveRoutingKey(mockRequest, {} as any);

            expect(result).toEqual(STG_ENV_NAME);
        });
    });

    describe('resolveRoutingKeyAsDev', () => {
        it('should resolve env suffix (as a routing key) from player id and return dev', async () => {
            const username = `${salt}_${userId}_${currency}`;
            const mockRequest = toMockPostRequest({ ['username']: username });

            const result = await service.resolveRoutingKey(mockRequest, {} as any);

            expect(result).toEqual(DEV_ENV_NAME);
        });
    });

    function toMockPostRequest(body: Record<string, string>): Request {
        return new Request('https://example.com', {
            method: 'POST',
            body: JSON.stringify(body),
            headers: {
                'Content-Type': 'application/json',
            },
        });
    }

    function toMockGetRequest(urlParams: string): Request {
        return new Request('https://example.com?' + urlParams, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
        });
    }
});