import {RoutingService} from "../../router.types";
import pragmatic, {TOKEN_PARAM, USER_ID_PARAM} from "./pragmatic";

describe('Pragmatic Routing Service', () => {
    let service: RoutingService = pragmatic;

    const routingKey: string = 'routingKey';
    const userId: string = '123';

    describe('resolveRoutingKey', () => {
        it('should resolve routing key from user id', async () => {
            const formData = new FormData();
            formData.append(USER_ID_PARAM, `${routingKey}_${userId}`);

            const result = await service.resolveRoutingKey(toMockRequest(formData), {} as any);

            expect(result).toEqual(routingKey);
        });

        it('should resolve routing key from token', async () => {
            const formData = new FormData();
            formData.append(TOKEN_PARAM, `${routingKey}/${userId}`);

            const result = await service.resolveRoutingKey(toMockRequest(formData), {} as any);

            expect(result).toEqual(routingKey);
        });
    });

    function toMockRequest(formData: FormData): any {
        return {
            formData: jest.fn().mockResolvedValue(formData)
        };
    }
});
