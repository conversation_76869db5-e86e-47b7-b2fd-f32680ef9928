import {RoutingService} from "../../router.types";

export const STG: string = 'xsttgx';
export const DEV_ENV_NAME: string = 'dev';
export const STG_ENV_NAME: string = 'stage';

const stg: RoutingService = {
    async resolveRoutingKey(request: Request, env: CloudflareEnv): Promise<string> {
        let requestPayload;

        if(request.method === 'GET') {
            requestPayload = request.url;
        } else {
            requestPayload = (await request.text()).toLowerCase();
        }

        return resolveRouting(requestPayload);
    }
}

function resolveRouting(requestPayload: string): string {
    return requestPayload.includes(STG) ? STG_ENV_NAME : DEV_ENV_NAME;
}

export default stg;