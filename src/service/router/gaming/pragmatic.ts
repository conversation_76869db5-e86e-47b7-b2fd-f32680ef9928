import {RoutingService} from "../../router.types";

export const USER_ID_PARAM: string = 'userId';
export const TOKEN_PARAM: string = 'token';

const pragmatic: RoutingService = {
    async resolveRoutingKey(request: Request, env: CloudflareEnv): Promise<string> {
        const form: FormData = await request.formData();

        const userId: string = form.get(USER_ID_PARAM);
        const token: string = form.get(TOKEN_PARAM);

        return !!userId ?
            resolveRoutingKeyFromUserId(userId) :
            resolveRoutingKeyFromToken(token);
    }
}

function resolveRoutingKeyFromToken(token: string): string {
    return token.split('/')[0];
}

function resolveRoutingKeyFromUserId(userId: string): string {
    return userId.split('_')[0];
}

export default pragmatic;