import {RoutingService} from "../../router.types";
import omni, {ROUTING_KEY_HEADER} from "./omni";

describe('Omni Routing Service', () => {
    let service: RoutingService = omni;

    const routingKey: string = 'routingKey';

    describe('resolveRoutingKey', () => {
        it('should resolve routing key from routingKey header', async () => {
            const result = await service.resolveRoutingKey(toMockRequest(), {} as any);

            expect(result).toEqual(routingKey);
        });
    });


    function toMockRequest(): any {
        return {
            headers: new Headers({
                [ROUTING_KEY_HEADER]: routingKey
            })
        };
    }
});
