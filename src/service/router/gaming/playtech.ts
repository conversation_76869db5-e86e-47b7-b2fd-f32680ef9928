import {RoutingService} from "../../router.types";

export const USER_NAME_PARAM: string = 'username';

const playtech: RoutingService = {
    async resolveRoutingKey(request: Request, env: CloudflareEnv): Promise<string> {
        const body = await request.json();

        const playerId: string = body[USER_NAME_PARAM];

        return resolveRoutingKeyFromPlayerId(playerId);
    }
}

function resolveRoutingKeyFromPlayerId(playerId: string): string {
    return playerId.split('_')[3];
}

export default playtech;
