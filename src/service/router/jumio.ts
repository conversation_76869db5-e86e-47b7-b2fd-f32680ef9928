import {RoutingService} from "../router.types";

export const TRANSACTION_PARAM: string = 'transaction';

const jumio: RoutingService = {
    async resolveRoutingKey(request: Request, env: CloudflareEnv): Promise<string> {
        const form: FormData = await request.formData();
        const transaction: string = form.get(TRANSACTION_PARAM);

        const json = JSON.parse(transaction);

        return json.customerId.split('/')[0];
    }
}

export default jumio;
