import {RoutingService} from "../router.types";
import jumio, {TRANSACTION_PARAM} from "./jumio";

describe('Jumio Routing Service', () => {
    let service: RoutingService = jumio;

    const routingKey: string = 'routingKey';
    const userId: string = '123';

    describe('resolveRoutingKey', () => {
        it('should resolve routing key from transaction', async () => {
            const result = await service.resolveRoutingKey(toMockRequest(), {} as any);

            expect(result).toEqual(routingKey);
        });
    });

    function toMockRequest(): any {
        const json = JSON.stringify({customerId: `${routingKey}/${userId}`})
        const formData = new FormData();
        formData.append(TRANSACTION_PARAM, json);

        return {
            formData: jest.fn().mockResolvedValue(formData)
        };
    }
});