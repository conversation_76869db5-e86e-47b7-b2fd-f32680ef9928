import {RoutingService} from "../../router.types";
import fiserv, {CLIENT_TOKEN_HEADER, NONCE_HEADER, PROVIDER_NAME} from "./fiserv";

describe('Fiserv Routing Service', () => {
    let service: RoutingService = fiserv;

    const routingKey: string = 'routingKey';
    const clientToken: string = 'token';
    const nonce: string = 'nonce';
    const transactionId: string = `${clientToken}_${nonce}`;

    describe('resolveRoutingKey', () => {
        it('should resolve routing key from client token and nonce', async () => {
            var mockEnv = toMockEnv();
            const result = await service.resolveRoutingKey(toMockRequest(), mockEnv);

            expect(mockEnv.KV_payment.get).toHaveBeenCalledWith(PROVIDER_NAME + '/' + transactionId);
            expect(result).toEqual(routingKey);
        });
    });

    function toMockRequest(): any {
        return {
            headers: new Headers({
                [CLIENT_TOKEN_HEADER]: clientToken,
                [NONCE_HEADER]: nonce
            })
        };
    }

    function toMockEnv(): CloudflareEnv {
        const get: any = jest.fn().mockReturnValue(routingKey);

        return {
            KV_payment: {
                get: get,
                list: jest.fn(),
                put: jest.fn(),
                getWithMetadata: jest.fn(),
                delete: jest.fn()
            }
        };
    }
});