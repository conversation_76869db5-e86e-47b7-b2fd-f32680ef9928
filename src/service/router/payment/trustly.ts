import {RoutingService} from "../../router.types";

export const PROVIDER_NAME: string = "trustly";
export const MERCHANT_REF_PARAM: string = 'merchantReference';
export const AUTH_PREFIX_TO_REMOVE: string = 'auth_';
export const REFRESH_PREFIX_TO_REMOVE: string = 'refresh_';
export const prefixPattern = new RegExp(`^(?:${AUTH_PREFIX_TO_REMOVE}|${REFRESH_PREFIX_TO_REMOVE}[A-Za-z]{3}_+)`);

const trustly: RoutingService = {
    async resolveRoutingKey(request: Request, env: CloudflareEnv): Promise<string> {
        const form: FormData = await request.formData();
        let reference: string = form.get(MERCHANT_REF_PARAM);
        reference = reference.replace(prefixPattern, '');
        console.log(`merchantReference: ${reference}`);

        return await env.KV_payment.get(PROVIDER_NAME + "/" + reference);
    }
}

export default trustly;