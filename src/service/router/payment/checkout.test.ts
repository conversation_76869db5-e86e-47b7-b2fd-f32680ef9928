import {RoutingService} from "../../router.types";
import checkout from "./checkout";

describe('Checkout Service', () => {
    let service: RoutingService = checkout;

    const routingKey: string = 'routingKey';

    describe('resolveRoutingKey', () => {
        it('should resolve routing key from transaction id', async () => {
            const result = await service.resolveRoutingKey(toMockRequest(), {} as any);

            expect(result).toEqual(routingKey);
        });
    });

    function toMockRequest(): any {
        const json = JSON.stringify({data: {metadata: {rk: `${routingKey}`}}});

        return {
            text: jest.fn().mockResolvedValue(json)
        };
    }

});