import {RoutingService} from "../../router.types";
import worldpay, {PROVIDER_NAME} from "./worldpay";

describe('Worldpay Routing Service', () => {
    let service: RoutingService = worldpay;

    const routingKey: string = 'routingKey';
    const orderCode: string = 'order123';

    describe('resolveRoutingKey', () => {
        it('should resolve routing key from order code', async () => {
            const mockEnv = toMockEnv();
            const result = await service.resolveRoutingKey(toMockRequest(), mockEnv);

            expect(mockEnv.KV_payment.get).toHaveBeenCalledWith(PROVIDER_NAME + '/' + orderCode);
            expect(result).toEqual(routingKey);
        });
    });

    function toMockRequest(): any {
        return {
            text: jest.fn().mockResolvedValue(`body start.. orderStatusEvent orderCode="${orderCode}" body end..`)
        };
    }

    function toMockEnv(): CloudflareEnv {
        const get: any = jest.fn().mockReturnValue(routingKey);

        return {
            KV_payment: {
                get: get,
                list: jest.fn(),
                put: jest.fn(),
                getWithMetadata: jest.fn(),
                delete: jest.fn()
            }
        };
    }
});