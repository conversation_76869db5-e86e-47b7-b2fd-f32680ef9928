import {RoutingService} from "../../router.types";

const paynearme: RoutingService = {
    async resolveRoutingKey(request: Request, env: CloudflareEnv): Promise<string> {
        const requestBody: string = await request.text();
        const payload = JSON.parse(requestBody);
        const siteCustomerIdentifier: string = payload.site_customer_identifier;
        console.log(`siteCustomerIdentifier id: ${siteCustomerIdentifier}`);

        return siteCustomerIdentifier.split("_")[0];
    }
}

export default paynearme;