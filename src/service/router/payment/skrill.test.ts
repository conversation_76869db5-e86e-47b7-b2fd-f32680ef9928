import skrill, {RK_PARAM} from "./skrill";
import {RoutingService} from "../../router.types";

describe('Skrill Routing Service', () => {
    let service: RoutingService = skrill;

    const routingKey: string = 'routingKey';
    const userId: string = '123';

    describe('resolveRoutingKey', () => {
        it('should resolve routing key from rk', async () => {
            const result = await service.resolveRoutingKey(toMockRequest(), {} as any);

            expect(result).toEqual(routingKey);
        });
    });

    function toMockRequest(): any {
        const formData = new FormData();
        formData.append(RK_PARAM, `${routingKey}/${userId}`);

        return {
            formData: jest.fn().mockResolvedValue(formData)
        };
    }
});