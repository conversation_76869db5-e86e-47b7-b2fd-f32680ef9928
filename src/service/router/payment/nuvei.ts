import {RoutingService} from "../../router.types";

export const USER_ID_PARAM: string = 'userid';

const nuvei: RoutingService = {
    async resolveRoutingKey(request: Request, env: CloudflareEnv): Promise<string> {
        const url: URL = new URL(request.url);
        const params: URLSearchParams = new URLSearchParams(url.search);
        const userId: string = params.get(USER_ID_PARAM);
        console.log(`userId: ${userId}`);

        return userId.split('_')[0];
    }
}

export default nuvei;

