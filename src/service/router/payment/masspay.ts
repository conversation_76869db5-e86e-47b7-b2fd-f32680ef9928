import {RoutingService} from "../../router.types";

const masspay: RoutingService = {
    async resolveRoutingKey(request: Request, env: CloudflareEnv): Promise<string> {
        const requestBody: string = await request.text();
        const json = JSON.parse(requestBody);
        const metadata = json.metadata.routingKey;
        console.log(`metadata: ${metadata}`);

        return metadata.split('/')[0];
    }
}

export default masspay;
