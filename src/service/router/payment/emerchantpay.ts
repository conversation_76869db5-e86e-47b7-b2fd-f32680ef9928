import {RoutingService} from "../../router.types";

export const PROVIDER_NAME: string = 'spreedly_emerchantpay';
export const TRANSACTION_ID_PARAM: string = 'transaction_id';

const emerchantpay: RoutingService = {
    async resolveRoutingKey(request: Request, env: CloudflareEnv): Promise<string> {
        const form: FormData = await request.formData();
        const transactionId: string = form.get(TRANSACTION_ID_PARAM);

        console.log(`transaction_id: ${transactionId}`);
        return await env.KV_payment.get(PROVIDER_NAME + '/' + transactionId);
    }
}

export default emerchantpay;