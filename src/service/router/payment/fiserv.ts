import {RoutingService} from "../../router.types";

export const PROVIDER_NAME: string = 'fiserv';
export const CLIENT_TOKEN_HEADER: string = 'Client-Token';
export const NONCE_HEADER: string = 'Nonce';

const fiserv: RoutingService = {
    async resolveRoutingKey(request: Request, env: CloudflareEnv): Promise<string> {
        const clientToken: string = request.headers.get(CLIENT_TOKEN_HEADER);
        const nonce: string = request.headers.get(NONCE_HEADER);
        const transactionId: string = clientToken + '_' + nonce;
        console.log(`transactionId: ${transactionId}`);

        return await env.KV_payment.get(PROVIDER_NAME + "/" + transactionId);
    }
}

export default fiserv;