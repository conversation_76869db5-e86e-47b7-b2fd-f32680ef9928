import {RoutingService} from "../../router.types";

const checkout: RoutingService = {
    async resolveRoutingKey(request: Request, env: CloudflareEnv): Promise<string> {
        const requestBody: string = await request.text();
        const payload = JSON.parse(requestBody);
        const rk: string = payload.data?.metadata?.rk;
        console.log(`RoutingKey: ${rk}`);

        return rk.split("/")[0];
    }
}

export default checkout;