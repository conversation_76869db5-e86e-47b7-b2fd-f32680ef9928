import masspay from "./masspay";
import {RoutingService} from "../../router.types";

describe('Masspay Routing Service', () => {
    let service: RoutingService = masspay;

    const routingKey: string = 'routingKey';
    const userId: string = '123';

    describe('resolveRoutingKey', () => {
        it('should resolve routing key from metadata', async () => {
            const result = await service.resolveRoutingKey(toMockRequest(), {} as any);

            expect(result).toEqual(routingKey);
        });
    });

    function toMockRequest(): any {
        const json = JSON.stringify({metadata: {routingKey: `${routingKey}/${userId}`}});

        return {
            text: jest.fn().mockResolvedValue(json)
        };
    }
});