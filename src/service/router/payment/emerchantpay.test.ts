import emerchantpay, {PROVIDER_NAME, TRANSACTION_ID_PARAM} from "./emerchantpay";
import {RoutingService} from "../../router.types";

describe('Emerchantpay Routing Service', () => {
    let service: RoutingService = emerchantpay;

    const routingKey: string = 'routingKey';
    const transactionId: string = 'tx123';

    describe('resolveRoutingKey', () => {
        it('should resolve routing key from transaction id', async () => {
            const mockEnv = toMockEnv();
            const result = await service.resolveRoutingKey(toMockRequest(), mockEnv);

            expect(mockEnv.KV_payment.get).toHaveBeenCalledWith(PROVIDER_NAME + '/' + transactionId);
            expect(result).toEqual(routingKey);
        });
    });

    function toMockRequest(): any {
        const formData = new FormData();
        formData.append(TRANSACTION_ID_PARAM, transactionId);

        return {
            formData: jest.fn().mockResolvedValue(formData)
        };
    }

    function toMockEnv(): CloudflareEnv {
        const get: any = jest.fn().mockReturnValue(routingKey);

        return {
            KV_payment: {
                get: get,
                list: jest.fn(),
                put: jest.fn(),
                getWithMetadata: jest.fn(),
                delete: jest.fn()
            }
        };
    }
});