import {RoutingService} from "../../router.types";

export const PROVIDER_NAME: string = "spreedly_worldpay";

const worldpay: RoutingService = {
    async resolveRoutingKey(request: Request, env: CloudflareEnv): Promise<string> {
        const requestBody: string = await request.text();
        const orderCodeRegex: RegExp = /orderStatusEvent orderCode="([^"]+)"/;
        const orderCode: string = requestBody.match(orderCodeRegex)[1];
        console.log(`transaction id: ${orderCode}`);

        return await env.KV_payment.get(PROVIDER_NAME + "/" + orderCode);
    }
}

export default worldpay;