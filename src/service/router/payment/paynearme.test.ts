import {RoutingService} from "../../router.types";
import paynearme from "./paynearme";

describe('Worldpay Routing Service', () => {
    let service: RoutingService = paynearme;

    const routingKey: string = 'routingKey';

    describe('resolveRoutingKey', () => {
        it('should resolve routing key from transaction id', async () => {
            const result = await service.resolveRoutingKey(toMockRequest(), {} as any);

            expect(result).toEqual(routingKey);
        });
    });

    function toMockRequest(): any {
        const json = JSON.stringify({site_customer_identifier: `${routingKey}`});

        return {
            text: jest.fn().mockResolvedValue(json)
        };
    }

});