import {RoutingService} from "../../router.types";
import rapyd from "./rapyd";

describe('Rapyd Routing Service', () => {
    let service: RoutingService = rapyd;

    const routingKey: string = 'routingKey';
    const userId: string = '123';

    describe('resolveRoutingKey', () => {
        it('should resolve routing key from metadata', async () => {
            const result = await service.resolveRoutingKey(toMockRequest(), {} as any);

            expect(result).toEqual(routingKey);
        });
    });

    function toMockRequest(): any {
        const json = JSON.stringify({data: {metadata: {routingKey: `${routingKey}/${userId}`}}});

        return {
            text: jest.fn().mockResolvedValue(json)
        };
    }
});