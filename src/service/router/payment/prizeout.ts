import {RoutingService} from "../../router.types";

const prizeout: RoutingService = {
    async resolveRoutingKey(request: Request, env: CloudflareEnv): Promise<string> {
        const requestBody: string = await request.text();
        const json = JSON.parse(requestBody);
        const sessionId = json.partner_session_id;
        console.log(`partner_session_id: ${sessionId}`)

        return sessionId.split('/')[0];
    }
}

export default prizeout;