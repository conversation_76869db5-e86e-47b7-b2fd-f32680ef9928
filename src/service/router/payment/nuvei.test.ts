import {RoutingService} from "../../router.types";
import nuvei, {USER_ID_PARAM} from './nuvei';

describe('Nuvei Routing Service', () => {
    let service: RoutingService = nuvei;

    const routingKey: string = 'routingKey';
    const userId: string = '123';

    describe('resolveRoutingKey', () => {
        it('should resolve routing key from user id', async () => {
            const result = await service.resolveRoutingKey(toMockRequest(), {} as any);

            expect(result).toEqual(routingKey);
        });

        function toMockRequest(): any {
            return {
                url: `http://localhost?${USER_ID_PARAM}=${routingKey}_${userId}`
            };
        }
    });
});
