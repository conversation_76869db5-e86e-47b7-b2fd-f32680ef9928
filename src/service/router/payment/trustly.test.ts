import {RoutingService} from "../../router.types";
import trustly, {MERCHANT_REF_PARAM, PROVIDER_NAME} from "./trustly";

describe('Trustly Routing Service', () => {
    let service: RoutingService = trustly;

    const routingKey: string = 'routingKey';
    const reference: string = 'ref123';

    describe('resolveRoutingKey', () => {
        it('should resolve routing key from merchant reference', async () => {
            const mockEnv = toMockEnv();
            const result = await service.resolveRoutingKey(toMockRequest(), mockEnv);

            expect(mockEnv.KV_payment.get).toHaveBeenCalledWith(PROVIDER_NAME + '/' + reference);
            expect(result).toEqual(routingKey);
        });
    });

    function toMockRequest(): any {
        const formData = new FormData();
        formData.append(MERCHANT_REF_PARAM, reference);

        return {
            formData: jest.fn().mockResolvedValue(formData)
        };
    }

    function toMockEnv(): CloudflareEnv {
        const get: any = jest.fn().mockReturnValue(routingKey);

        return {
            KV_payment: {
                get: get,
                list: jest.fn(),
                put: jest.fn(),
                getWithMetadata: jest.fn(),
                delete: jest.fn()
            }
        };
    }
});