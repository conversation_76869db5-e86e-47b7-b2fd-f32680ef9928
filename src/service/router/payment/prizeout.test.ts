import {RoutingService} from "../../router.types";
import prizeout from "./prizeout";

describe('Prizeout Routing Service', () => {
    let service: RoutingService = prizeout;

    const routingKey: string = 'routingKey';
    const userId: string = '123';

    describe('resolveRoutingKey', () => {
        it('should resolve routing key from partner session id', async () => {
            const result = await service.resolveRoutingKey(toMockRequest(), {} as any);

            expect(result).toEqual(routingKey);
        });
    });

    function toMockRequest(): any {
        const json = JSON.stringify({partner_session_id: `${routingKey}/${userId}`});

        return {
            text: jest.fn().mockResolvedValue(json)
        };
    }
});