import { RoutingService } from "../../router.types";

export const VENDOR_DATA_FIELD: string = "vendorData";

const veriff: RoutingService = {
    async resolveRoutingKey(request: Request, env: CloudflareEnv): Promise<string> {
        const body: any = await request.json();

        const vendorData = body[VENDOR_DATA_FIELD] || (body.verification ? body.verification[VENDOR_DATA_FIELD] : null);
        if (!vendorData) {
            throw new Error("Missing 'vendorData' in the request body or inside the 'verification' object.");
        }

        const segments = vendorData.split('/');
        if (segments.length < 4) {
            throw new Error(`Invalid vendorData format (expected at least 4 parts): "${vendorData}"`);
        }

        let userHash = segments[1];
        if (userHash.endsWith("xsttgx")) {
            userHash = userHash.slice(0, -6);
        }

        const possibleEnv = segments.length > 5 ? segments[5] : null;
        const routingKey = possibleEnv ? (userHash + possibleEnv) : userHash;

        console.log(`Routing Key = ${routingKey}`);

        return routingKey;
    }
};

export default veriff;
