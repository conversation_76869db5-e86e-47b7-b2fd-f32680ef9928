import { RoutingService } from "../../router.types";
import veriff from "./veriff";

describe('Veriff Routing Service', () => {
    let service: RoutingService = veriff;

    const userId = "myUser";
    const userHash = "myHash";
    const userHashWithSuffix = "myHashxsttgx";
    const transactionId = "tx123";
    const brandName = "someBrand";
    const country = "us";
    const env = "stage";

    const minimalVendorData = `${userId}/${userHash}/${transactionId}/${brandName}`;
    const minimalVendorDataWithSuffix = `${userId}/${userHashWithSuffix}/${transactionId}/${brandName}`;
    const fullVendorData = `${userId}/${userHash}/${transactionId}/${brandName}/${country}/${env}`;
    const fullVendorDataWithSuffix = `${userId}/${userHashWithSuffix}/${transactionId}/${brandName}/${country}/${env}`;
    const vendorDataWithoutCountry = `${userId}/${userHash}/${transactionId}/${brandName}//${env}`;
    const vendorDataWithoutCountryWithSuffix = `${userId}/${userHashWithSuffix}/${transactionId}/${brandName}//${env}`;

    describe('resolveRoutingKey', () => {
        it('should return just userHash if no env is present (vendorData inside verification)', async () => {
            const mockRequest = toMockPostRequest({ verification: { vendorData: minimalVendorData } });

            const routingKey = await service.resolveRoutingKey(mockRequest, {} as any);
            expect(routingKey).toEqual(userHash);
        });

        it('should return just userHash if no env is present (vendorData at root level)', async () => {
            const mockRequest = toMockPostRequest({ vendorData: minimalVendorData });

            const routingKey = await service.resolveRoutingKey(mockRequest, {} as any);
            expect(routingKey).toEqual(userHash);
        });

        it('should return userHash + env if env is present (vendorData inside verification)', async () => {
            const mockRequest = toMockPostRequest({ verification: { vendorData: fullVendorData } });

            const routingKey = await service.resolveRoutingKey(mockRequest, {} as any);
            expect(routingKey).toEqual(userHash + env);
        });

        it('should return userHash + env if env is present (vendorData at root level)', async () => {
            const mockRequest = toMockPostRequest({ vendorData: fullVendorData });

            const routingKey = await service.resolveRoutingKey(mockRequest, {} as any);
            expect(routingKey).toEqual(userHash + env);
        });

        it('should return userHash + env if country is not present (vendorData inside verification)', async () => {
            const mockRequest = toMockPostRequest({ verification: { vendorData: vendorDataWithoutCountry } });

            const routingKey = await service.resolveRoutingKey(mockRequest, {} as any);
            expect(routingKey).toEqual(userHash + env);
        });

        it('should return userHash + env if country is not present (vendorData at root level)', async () => {
            const mockRequest = toMockPostRequest({ vendorData: vendorDataWithoutCountry });

            const routingKey = await service.resolveRoutingKey(mockRequest, {} as any);
            expect(routingKey).toEqual(userHash + env);
        });

        it('should throw an error if vendorData has fewer than 4 segments (vendorData inside verification)', async () => {
            const invalidData = `${userId}/${userHash}/${transactionId}`;

            const mockRequest = toMockPostRequest({ verification: { vendorData: invalidData } });
            await expect(service.resolveRoutingKey(mockRequest, {} as any))
                .rejects
                .toThrowError();
        });

        it('should throw an error if vendorData has fewer than 4 segments (vendorData at root level)', async () => {
            const invalidData = `${userId}/${userHash}/${transactionId}`;

            const mockRequest = toMockPostRequest({ vendorData: invalidData });
            await expect(service.resolveRoutingKey(mockRequest, {} as any))
                .rejects
                .toThrowError();
        });

        it('should throw an error if vendorData is missing from both root and verification', async () => {
            const mockRequest = toMockPostRequest({});

            await expect(service.resolveRoutingKey(mockRequest, {} as any))
                .rejects
                .toThrowError("Missing 'vendorData' in the request body or inside the 'verification' object.");
        });

        it('should remove "xsttgx" from userHash when no env is present', async () => {
            const mockRequest = toMockPostRequest({ vendorData: minimalVendorDataWithSuffix });

            const routingKey = await service.resolveRoutingKey(mockRequest, {} as any);
            expect(routingKey).toEqual(userHash);
        });

        it('should remove "xsttgx" from userHash and return userHash + env if env is present', async () => {
            const mockRequest = toMockPostRequest({ vendorData: fullVendorDataWithSuffix });

            const routingKey = await service.resolveRoutingKey(mockRequest, {} as any);
            expect(routingKey).toEqual(userHash + env);
        });

        it('should remove "xsttgx" from userHash and return userHash + env if country is not present', async () => {
            const mockRequest = toMockPostRequest({ vendorData: vendorDataWithoutCountryWithSuffix });

            const routingKey = await service.resolveRoutingKey(mockRequest, {} as any);
            expect(routingKey).toEqual(userHash + env);
        });
    });

    function toMockPostRequest(body: object): Request {
        return new Request('https://example.com', {
            method: 'POST',
            body: JSON.stringify(body),
            headers: {
                'Content-Type': 'application/json',
            },
        });
    }
});
