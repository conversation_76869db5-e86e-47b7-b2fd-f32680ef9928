import {fetchReq, toNormalizedURL} from "../util/utils";
import {Context} from "hono";
import {getCookie} from "hono/cookie";

const cache: Cache = caches.default;

export const CFTRACE_COOKIE_NAME = "_cftrace";
export const SID = "sid";

export async function handleRequestWithCache(ctx: Context): Promise<Response> {
    const request: Request = ctx.req.raw;
    const url: URL = toNormalizedURL(request);

    const cacheKey: URL = new URL(url);
    const cached: Response = await cache.match(cacheKey);

    if (cached) {
        console.log('cache hit');
        return cached;
    }

    console.log('cache miss');
    return fetchAndCache(request, cacheKey);
}

export async function handleRequestWithCacheControl(ctx: Context): Promise<Response> {
    const request: Request = ctx.req.raw;
    const url: URL = toNormalizedURL(request);

    const sid = getSessionId(ctx);
    const cftrace = getCookie(ctx, CFTRACE_COOKIE_NAME);

    if (sid && cftrace) {
        let params = new URLSearchParams(url.searchParams);
        params.set(CFTRACE_COOKIE_NAME, cftrace);
        params.set(SID, sid);

        const cacheKey = new URL(url);
        cacheKey.search = params.toString();

        const cached: Response = await cache.match(cacheKey);

        if (cached) {
            console.log('cache hit');
            return cached;
        }

        console.log('cache miss');
        return fetchAndCacheWithControl(request, cacheKey);
    }

    return fetchReq(request);
}

export async function fetchAndCache(request: Request, cacheKey: Request | URL): Promise<Response> {
    const response: Response = await fetchReq(request);

    if (response.status === 200) {
        response.headers.set("Cache-Control", "s-maxage=120");

        console.log(`cache put`);
        await cache.put(cacheKey, prepareToCache(response.clone()));
    }

    return response;
}

export async function fetchAndCacheWithControl(request: Request, cacheKey: Request | URL): Promise<Response> {
    const response: Response = await fetchReq(request);

    if (response.status === 200) {
        const cacheControl = response.headers.get("Cache-Control");

        if (cacheControl) {
            console.log(`cache put`);
            await cache.put(cacheKey, prepareToCache(response.clone()));
        }
    }

    return response;
}

function prepareToCache(response: Response): Response {
    if (response.headers.has("Set-Cookie")) {
        const headers = new Headers(response.headers);
        headers.delete("Set-Cookie");

        return new Response(response.body, {
            status: response.status,
            statusText: response.statusText,
            headers: headers
        });
    }

    return response;
}

function getSessionId(ctx: Context): string {
    const url = new URL(ctx.req.url);
    const brandName = url.searchParams.get("brandName");

    if (brandName) {
        const authCookie = (brandName + SID).toUpperCase();
        return getCookie(ctx, authCookie);
    }
}