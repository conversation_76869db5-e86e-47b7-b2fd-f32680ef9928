import {Context, Next} from "hono";
import {
    CF_WORKER_HEADER,
    filterAnonymousUser,
    filterDevEnv,
    handleCfWorkerHeader,
    isDevEnv,
    isUserAuthorized,
    LOCALHOST
} from "./filters";
import {DEVELOPMENT_ENV} from "./router";
import * as utils from "../util/utils";

const COOKIE_HEADER: string = "Cookie";
const PRODUCTION_ENV: string = "prod";

describe('Filters', () => {
    const authCookie: string = "BLUEDREAMSID=123";
    const fetch = jest.spyOn(utils, 'fetchReq').mockResolvedValue(new Response('fetched', {status: 200}));

    afterEach(() => {
        fetch.mockClear();
    });

    function getUrl(env: string, host: string = 'example'): string {
        return `https://${env}-service.${host}.com/test?brandName=bluedream`;
    }

    it('should test filterAnonymousUser function is anonymous', async () => {
        const url: string = getUrl(DEVELOPMENT_ENV);

        const ctx: Context = mockCtx(url, {});
        const next: Next = jest.fn();

        await filterAnonymousUser(ctx, next);

        expect(fetch).not.toHaveBeenCalled();
        expect(next).toHaveBeenCalled();
    });

    it('should test filterAnonymousUser function is authorized', async () => {
        const url: string = getUrl(DEVELOPMENT_ENV);

        const ctx: Context = mockCtx(url, {[COOKIE_HEADER]: authCookie});
        const next: Next = jest.fn();

        await filterAnonymousUser(ctx, next);

        expect(fetch).toHaveBeenCalled();
        expect(next).not.toHaveBeenCalled();
    });

    it('should test filterDevEnv function is dev', async () => {
        const url: string = getUrl(DEVELOPMENT_ENV);

        const ctx: Context = mockCtx(url, {}) as Context;
        const next: Next = jest.fn();

        await filterDevEnv(ctx, next);

        expect(fetch).not.toHaveBeenCalled();
        expect(next).toHaveBeenCalled();
    });

    it('should test filterDevEnv function is prod', async () => {
        const url: string = getUrl(PRODUCTION_ENV);

        const ctx: Context = mockCtx(url, {}) as Context;
        const next: Next = jest.fn();

        await filterDevEnv(ctx, next);

        expect(fetch).toHaveBeenCalled();
        expect(next).not.toHaveBeenCalled();
    });

    // should be not localhost
    it('should test handleCfWorkerHeader function is not localhost', async () => {
        const url: string = getUrl(DEVELOPMENT_ENV);

        const ctx: Context = mockCtx(url, {}) as Context;
        const next: Next = jest.fn();

        await handleCfWorkerHeader(ctx, next);
        expect(next).toHaveBeenCalled();
        expect(ctx.req.header(CF_WORKER_HEADER)).toBeNull()
    });

    // should be localhost
    it('should test handleCfWorkerHeader function is localhost', async () => {
        const url: string = getUrl(DEVELOPMENT_ENV, LOCALHOST);

        const ctx: Context = mockCtx(url, {}) as Context;
        const next: Next = jest.fn();

        await handleCfWorkerHeader(ctx, next);
        expect(next).toHaveBeenCalled();
        expect(ctx.req.header(CF_WORKER_HEADER)).toEqual(LOCALHOST)
    });

    // should be localhost with cf-worker header
    it('should test handleCfWorkerHeader function fetch origin', async () => {
        const url: string = getUrl(DEVELOPMENT_ENV, LOCALHOST);

        const ctx: Context = mockCtx(url, {[CF_WORKER_HEADER]: LOCALHOST}) as Context;
        const next: Next = jest.fn();

        const result = await handleCfWorkerHeader(ctx, next);

        expect(next).not.toHaveBeenCalled();
        expect(result).toEqual('dev-service.localhost.com origin request');
    });

    it('should test isUserAuthorized function is authorized', () => {
        const url: string = getUrl(DEVELOPMENT_ENV);

        const ctx: Context = mockCtx(url, {[COOKIE_HEADER]: authCookie}) as Context;

        const result = isUserAuthorized(ctx);

        expect(result).toEqual(true);
    });

    it('should test isUserAuthorized function is anonymous', () => {
        const url: string = getUrl(DEVELOPMENT_ENV);

        const ctx: Context = mockCtx(url, {}) as Context;

        const result = isUserAuthorized(ctx);

        expect(result).toEqual(false);
    });

    it('should test isDevEnv function dev', () => {
        const result = isDevEnv(getUrl(DEVELOPMENT_ENV));

        expect(result).toEqual(true);
    });

    it('should test isDevEnv function prod', () => {
        const result = isDevEnv(getUrl(PRODUCTION_ENV));

        expect(result).toEqual(false);
    });

    function mockCtx(url: string, headers: {}): Context {
        return {
            req: {
                url: url,
                method: 'GET',
                raw: {
                    url: url,
                    headers: new Headers(headers),
                    body: null
                },
                header: function (headerName: string) {
                    return this.raw.headers.get(headerName);
                }
            },
            text: function (text: any) {
                return text;
            }
        } as Context;
    }
});
