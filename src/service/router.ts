import {fetchReq, PAYMENT_GATEWAY, AGGREGATOR, FRAUD_GATEWAY} from "../util/utils";
import {Context, Env} from "hono";
import {RoutingService} from "./router.types";
import {LOCALHOST} from "./filters";
import {envConfig} from "../config/aggregator-config";

export const HTTP_PROTOCOL: string = 'http';
export const HTTPS_PROTOCOL: string = 'https';
export const DEVELOPMENT_ENV: string = 'dev';
export const STAGE_ENV: string = 'stage';
export const STAGE_ROUTING_KEY_SUFFIX: string = 'xsttgx';
export const PRODUCTION_ENV: string = 'prod';
const GATEWAY_HOST: string = '{protocol}://{env}-gateway.{host}';
const PAYMENT_GATEWAY_HOST: string = '{protocol}://{env}-payment-gateway.{host}';
const FRAUD_GATEWAY_HOST: string = '{protocol}://{env}-fraud-gateway.{host}';
const AGGREGATOR_HOST: string = '{protocol}://{env}.{host}';
const TIMEOUT: number = 30_000;

export async function routeRequest(ctx: Context, service: RoutingService): Promise<Response> {
    const request: Request = ctx.req.raw;
    const url: URL = new URL(request.url);
    console.log(`route request: ${url.pathname}`);

    const routingKey: string = await service.resolveRoutingKey(request.clone(), ctx.env);
    console.log(`routing key: ${routingKey}`)

    const routeRequest: Request = toRouteRequest(request, url, routingKey);
    return await fetchReq(routeRequest);
}

export function toRouteRequest(request: Request, url: URL, routingKey: string): Request {
    const routeUrl = getRouteURL(routingKey, url);
    console.log(`routeUrl: ${routeUrl}`);

    return toRequest(routeUrl, request);
}

export function getRouteURL(routingKey: string, url: URL) {
    const host: string = getRouteHost(routingKey, url.host);
    const routeUrl: URL = new URL(host + url.pathname);

    const params: URLSearchParams = new URLSearchParams(url.search);
    params.forEach((value: string, key: string) => {
        routeUrl.searchParams.append(key, value);
    });

    return routeUrl;
}

export function getRouteHost(routingKey: string, host: string): string {
    let template: string;
    let env: string;
    let domain: string;

    if (host.includes(PAYMENT_GATEWAY.getName())) {
        template = PAYMENT_GATEWAY_HOST;
        env = getEnv(routingKey);
        domain = getPaymentDomain(env, host);
    } else if (host.includes(AGGREGATOR.getName())) {
        template = AGGREGATOR_HOST;
        env = getAggregatorEnv(routingKey, host);
        domain = getAggregatorDomain(routingKey, host);
    } else if (host.includes(FRAUD_GATEWAY.getName())) {
        template = FRAUD_GATEWAY_HOST;
        env = getEnv(routingKey);
        domain = getFraudDomain(env, host);
    } else {
        template = GATEWAY_HOST;
        env = getEnv(routingKey)
        domain = getMainDomain(host);
    }

    return template
        .replace('{protocol}', getProtocol(host))
        .replace('{env}', env)
        .replace('{host}', domain);
}

export function getEnv(routingKey: string): string {
    const env: string = routingKey.substring(8);

    if (env === STAGE_ROUTING_KEY_SUFFIX) {
        return STAGE_ENV
    }

    return env.length > 0 ? env : DEVELOPMENT_ENV;
}

export function getAggregatorEnv(routingKey: string, host: string): string {
    const env = host.includes(DEVELOPMENT_ENV) ? DEVELOPMENT_ENV : PRODUCTION_ENV;

    return envConfig[env][routingKey] || `${env}-${AGGREGATOR.getName()}`;
}

export function getMainDomain(host: string): string {
    const slice = host.includes(LOCALHOST) ? -1 : -2;

    return host.split('.').slice(slice).join('.');
}

export function getFraudDomain(env: string, host: string): string {
    const domain = getMainDomain(host);

    if (env === STAGE_ENV) {
        return domain.replaceAll(DEVELOPMENT_ENV, STAGE_ENV);
    }

    return domain;
}

export function getPaymentDomain(env: string, host: string): string {
    const domain = getMainDomain(host);

    if (env === STAGE_ENV) {
        return domain.replaceAll(DEVELOPMENT_ENV, STAGE_ENV);
    }

    return domain;
}

export function getAggregatorDomain(routingKey: string, host: string): string {
     const domain = getMainDomain(host);

      if (routingKey === STAGE_ENV) {
          return domain.replaceAll('patrianna.com', 'stage-b2spin.com');
      }

      return domain;
}

export function getProtocol(host: string): string {
    return host.includes(LOCALHOST) ? HTTP_PROTOCOL : HTTPS_PROTOCOL;
}

export function toRequest(routeUrl: URL, request: Request): Request {
    const method = request.method;

    const init: RequestInit = {
        method: request.method,
        headers: request.headers,
        signal: AbortSignal.timeout(TIMEOUT),
    };

    if (method !== 'GET' && method !== 'HEAD') {
        init.body = request.body;
    }

    return new Request(routeUrl.toString(), init);
}