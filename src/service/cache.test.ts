import {CFTRACE_COOKIE_NAME, handleRequestWithCache, handleRequestWithCacheControl, SID} from "./cache";
import {Context} from "hono";
import * as utils from "../util/utils";
import {Readable} from "stream";

describe('Cache', () => {
    it('should test handleRequestWithCacheControl on cache hit', async () => {
        const url = new URL('https://example.com/test?brandName=bluedream');
        const cachedResponse = new Response('cached', {status: 200});

        const mockMatch = jest.fn().mockResolvedValue(cachedResponse);
        caches.default.match = mockMatch;

        let cftrace = 'trace123';
        let sid = 'sid123';

        const key = new URL(url);
        const params = new URLSearchParams(url.searchParams);
        params.set(CFTRACE_COOKIE_NAME, cftrace);
        params.set(SID, sid);

        key.search = params.toString();

        const headers = {'Cache-Control': 's-maxage=120', 'Cookie': `_cftrace=${cftrace}; BLUEDREAMSID=${sid}`};
        const result = await handleRequestWithCacheControl(mockCtxPOST(url.toString(), headers));

        expect(mockMatch).toHaveBeenCalledWith(key);
        expect(result).toEqual(cachedResponse);
    });

    it('should test handleRequestWithCacheControl on cache miss', async () => {
        const url = new URL('https://example.com/test?brandName=bluedream');
        const cachedResponse = new Response('cached', {status: 200});

        const mockResp = new Response('fetched', {status: 200});
        const fetch = jest.spyOn(utils, 'fetchReq').mockResolvedValue(mockResp);

        const mockMatch = jest.fn().mockResolvedValue(null);
        caches.default.match = mockMatch;

        let cftrace = 'trace123';
        let sid = 'sid123';

        const key = new URL(url);
        const params = new URLSearchParams(url.searchParams);
        params.set(CFTRACE_COOKIE_NAME, cftrace);
        params.set(SID, sid);

        key.search = params.toString();

        const headers = {'Cache-Control': 's-maxage=120', 'Cookie': `_cftrace=${cftrace}; BLUEDREAMSID=${sid}`};
        const result = await handleRequestWithCacheControl(mockCtxPOST(url.toString(), headers));

        expect(mockMatch).toHaveBeenCalledWith(key);
        expect(fetch).toHaveBeenCalledWith(expect.objectContaining({url: url.toString()}));
        expect(result).toEqual(mockResp);
    });

    it('should test handleRequestWithCacheControl not auth', async () => {
        const url = new URL('https://example.com/test?brandName=bluedream');
        const cachedResponse = new Response('cached', {status: 200});

        const mockResp = new Response('fetched', {status: 200});
        const fetch = jest.spyOn(utils, 'fetchReq').mockResolvedValue(mockResp);

        const mockMatch = jest.fn().mockResolvedValue(cachedResponse);
        caches.default.match = mockMatch;

        let cftrace = 'trace123';

        const headers = {'Cache-Control': 's-maxage=120', 'Cookie': `_cftrace=${cftrace};`};
        const result = await handleRequestWithCacheControl(mockCtxPOST(url.toString(), headers));

        expect(mockMatch).not.toHaveBeenCalled();
        expect(fetch).toHaveBeenCalledWith(expect.objectContaining({url: url.toString()}));
        expect(result).toEqual(mockResp);
    });

    it('should test handleRequestWithCache on cache hit', async () => {
        const url = new URL('https://example.com/test?brandName=bluedream');
        const cachedResponse = new Response('cached', {status: 200});

        const mockMatch = jest.fn().mockResolvedValue(cachedResponse);
        caches.default.match = mockMatch;

        const result = await handleRequestWithCache(mockCtxGET(url.toString(), {}));

        expect(mockMatch).toHaveBeenCalledWith(url);
        expect(result).toEqual(cachedResponse);
    });

    it('should test handleRequestWithCache on cache hit corrupted', async () => {
        const url = new URL('https://example.com/test?brandName=bluedream');
        const cachedResponse = new Response('cached', {status: 200});

        const mockMatch = jest.fn().mockResolvedValue(cachedResponse);
        caches.default.match = mockMatch;

        const result = await handleRequestWithCache(mockCtxGET(`${url.toString()}&a=123`, {}));

        expect(mockMatch).toHaveBeenCalledWith(url);
        expect(result).toEqual(cachedResponse);
    });

    it('should test handleRequestWithCache on cache miss', async () => {
        const mockResp = new Response('fetched', {status: 200});
        const fetch = jest.spyOn(utils, 'fetchReq').mockResolvedValue(mockResp);

        const url = new URL('https://example.com/test?brandName=bluedream');

        const mockMatch = jest.fn().mockResolvedValue(null);
        caches.default.match = mockMatch;

        const mockPut = jest.fn();
        caches.default.put = mockPut;

        const result = await handleRequestWithCache(mockCtxGET(url.toString(), {}));

        expect(mockMatch).toHaveBeenCalledWith(url);
        expect(fetch).toHaveBeenCalledWith(expect.objectContaining({url: url.toString()}));
        expect(mockPut).toHaveBeenCalled();
    });

    it('should test handleRequestWithCache on cache miss error', async () => {
        const mockResp = new Response('fetched', {status: 400});
        const fetch = jest.spyOn(utils, 'fetchReq').mockResolvedValue(mockResp);

        const url = new URL('https://example.com/test?brandName=bluedream');

        const mockMatch = jest.fn().mockResolvedValue(null);
        caches.default.match = mockMatch;

        const mockPut = jest.fn();
        caches.default.put = mockPut;

        const result = await handleRequestWithCache(mockCtxGET(url.toString(), {}));

        expect(mockMatch).toHaveBeenCalledWith(url);
        expect(fetch).toHaveBeenCalledWith(expect.objectContaining({url: url.toString()}));
        expect(mockPut).not.toHaveBeenCalled();
    });

    function mockCtxGET(url: string, headers: {}): Context {
        const body: Readable = Readable.from('test').pipe(process.stdout);

        return mockCtx(url, headers, 'GET', null);
    }

    function mockCtxPOST(url: string, headers: {}): Context {
        const body: Readable = Readable.from('test').pipe(process.stdout);

        return mockCtx(url, headers, 'POST', body);
    }

    function mockCtx(url: string, headers: {}, method: string, body: any): Context {
        return {
            req: {
                url: url,
                method: method,
                raw: {
                    url: url,
                    headers: new Headers(headers),
                    body: body
                },
                header: function (headerName: string) {
                    return this.raw.headers.get(headerName);
                }
            },
            text: function (text: any) {
                return text;
            }
        } as Context;
    }
});