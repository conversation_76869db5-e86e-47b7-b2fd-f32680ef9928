import {Context, Next} from "hono";
import {fetchReq} from "../util/utils";
import {getCookie} from "hono/cookie";
import {DEVELOPMENT_ENV} from "./router";

export const CF_WORKER_HEADER: string = 'cf-worker';
export const LOCALHOST: string = 'localhost';

export async function filterAnonymousUser(ctx: Context, next: Next): Promise<Response> {
    try {
        if (isUserAuthorized(ctx)) {
            return await fetchOrigin(ctx);
        }
    } catch (error) {
        return ctx.text(error);
    }

    await next();
}

export async function filterDevEnv(ctx: Context, next: Next): Promise<Response> {
    const url = new URL(ctx.req.url);

    if (!isDevEnv(url.hostname)) {
        return await fetchReq(ctx.req.raw);
    }

    await next();
}

export async function fetchOrigin(ctx: Context): Promise<Response> {
    return await fetchReq(ctx.req.raw);
}

export async function handleCfWorkerHeader(ctx: Context, next: Next): Promise<Response> {
    const url: URL = new URL(ctx.req.url);

    if (url.hostname.includes(LOCALHOST)) { // only for local env, cf manages it by itself
        if (ctx.req.header(CF_WORKER_HEADER)) {
            return ctx.text(`${url.hostname} origin request`);
        }

        const headers = new Headers(ctx.req.raw.headers);
        headers.append(CF_WORKER_HEADER, LOCALHOST);

        ctx.req.raw = new Request(ctx.req.url, {
            method: ctx.req.method,
            headers: headers,
            body: ctx.req.raw.body
        });
    }

    await next();
}

export function isUserAuthorized(ctx: Context): boolean {
    const url = new URL(ctx.req.url);
    const brandName = url.searchParams.get("brandName");

    if (brandName) {
        const authCookie = (brandName + "sid").toUpperCase();
        return !!getCookie(ctx, authCookie);
    }

    throw new Error("brandName is missing");
}

export function isDevEnv(hostname: string): boolean {
    return hostname.includes(DEVELOPMENT_ENV);
}
