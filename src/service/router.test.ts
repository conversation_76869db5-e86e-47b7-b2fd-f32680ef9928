import {
    DEVELOPMENT_ENV,
    getAggregatorEnv,
    getEnv,
    getMainDomain,
    getProtocol,
    getRouteHost,
    getRouteURL,
    HTTP_PROTOCOL,
    HTTPS_PROTOCOL,
    routeRequest,
    toRequest,
    toRouteRequest
} from './router';
import {Context} from "hono";
import {RoutingService} from "./router.types";
import {LOCALHOST} from "./filters";
import * as utils from "./../util/utils";

describe('Test suite for the provided code', () => {
    const fetch = jest.spyOn(utils, 'fetchReq').mockResolvedValue(new Response('fetched', {status: 200}));

    afterEach(() => {
        fetch.mockClear();
    });

    it('should test routeRequest function', async () => {
        const url: string = 'https://dev-gateway.example.com/test?brandName=bluedream';

        const ctx: Context = mockCtx(url, {});

        await routeRequest(ctx, mockRoutingService())

        expect(fetch).toHaveBeenCalledWith(expect.objectContaining({
            url: 'https://stage-gateway.example.com/test?brandName=bluedream'
        }));
    });

    it('should test toRouteRequest function', () => {
        const routingKey: string = 'abcd1234xsttgx';
        const url: URL = new URL('https://dev-gateway.example.com/test?brandName=bluedream');
        const request: Request = new Request(url.toString(), {
            method: 'POST',
            headers: new Headers({['test_header']: 'test_value'}),
            body: 'test'
        });

        const result: Request = toRouteRequest(request.clone(), url, routingKey);
        expect(result.url).toEqual('https://stage-gateway.example.com/test?brandName=bluedream');
        expect(result.method).toEqual(request.method);
        expect(result.text()).toEqual(request.text());
    });

    it('should test getRouteURL function', () => {
        const routingKey: string = 'abcd1234xsttgx';
        const url: URL = new URL('https://dev-gateway.example.com/test?brandName=bluedream');

        const result = getRouteURL(routingKey, url);
        expect(result.toString()).toEqual('https://stage-gateway.example.com/test?brandName=bluedream')
    });

    it('should test getRouteHost function with payment-gateway', () => {
        const routingKey: string = 'abcd1234xsttgx';
        const host: string = 'dev-payment-gateway.example.com';

        const result = getRouteHost(routingKey, host);
        expect(result).toEqual('https://stage-payment-gateway.example.com')
    });

    it('should test getRouteHost function with payment-gateway with dev host', () => {
        const routingKey: string = 'abcd1234xsttgx';
        const host: string = 'dev-payment-gateway.dev-example.com';

        const result = getRouteHost(routingKey, host);
        expect(result).toEqual('https://stage-payment-gateway.stage-example.com')
    });

    it('should test getRouteHost function with gateway', () => {
        const routingKey: string = 'abcd1234xsttgx';
        const host: string = 'dev-gateway.example.com';

        const result = getRouteHost(routingKey, host);
        expect(result).toEqual('https://stage-gateway.example.com')
    });


    it('should test getRouteHost function with gateway localhost', () => {
        const routingKey: string = 'abcd1234xsttgx';
        const host: string = 'dev-gateway.localhost';

        const result = getRouteHost(routingKey, host);
        expect(result).toEqual('http://stage-gateway.localhost')
    });

    it('should test getRouteHost function with aggregator spree dev', () => {
        const routingKey: string = 'sr';
        const host: string = 'dev-aggregator.example.com';

        const result = getRouteHost(routingKey, host);
        expect(result).toEqual('https://dev-spree-aggregator.example.com')
    });

    it('should test getRouteHost function with aggregator spree prod', () => {
        const routingKey: string = 'sr';
        const host: string = 'prod-aggregator.example.com';

        const result = getRouteHost(routingKey, host);
        expect(result).toEqual('https://prod-spree-aggregator.example.com')
    });

    it('should test getRouteHost function with aggregator patbit dev', () => {
        const routingKey: string = 'pt';
        const host: string = 'dev-aggregator.example.com';

        const result = getRouteHost(routingKey, host);
        expect(result).toEqual('https://dev-aggregator.example.com')
    });

    it('should test getRouteHost function with aggregator patbit prod', () => {
        const routingKey: string = 'pt';
        const host: string = 'prod-aggregator.example.com';

        const result = getRouteHost(routingKey, host);
        expect(result).toEqual('https://prod-b2b-pipe.example.com')
    });

    it('should test getRouteHost function with aggregator mcluck prod', () => {
        const routingKey: string = 'ml';
        const host: string = 'prod-aggregator.example.com';

        const result = getRouteHost(routingKey, host);
        expect(result).toEqual('https://prod-aggregator.example.com')
    });

    it('should test getEnv function with enhanced rk', () => {
        const routingKey: string = 'abcd1234xsttgx';

        const result = getEnv(routingKey);

        expect(result).toEqual('stage');
    });

    it('should test getEnv function with default rk', () => {
        const routingKey: string = 'abcd1234';

        const result = getEnv(routingKey);

        expect(result).toEqual(DEVELOPMENT_ENV);
    });

    it('should test getEnv function with default rk', () => {
        const routingKey: string = 'abcd1234';

        const result = getEnv(routingKey);

        expect(result).toEqual(DEVELOPMENT_ENV);
    });

    it('should test getMainDomain function with localhost', () => {
        const host: string = 'dev-service.app.localhost';

        const result = getMainDomain(host);

        expect(result).toEqual('localhost')
    });

    it('should test getMainDomain function with not localhost', () => {
        const host: string = 'dev-service.app.example.com';

        const result = getMainDomain(host);

        expect(result).toEqual('example.com')
    });

    it('should test getProtocol function with localhost', () => {
        const result = getProtocol(LOCALHOST);

        expect(result).toEqual(HTTP_PROTOCOL);
    });

    it('should test getProtocol function with not localhost', () => {
        const host: string = "example.com";

        const result = getProtocol(host);

        expect(result).toEqual(HTTPS_PROTOCOL);
    });

    it('should test toRequest', () => {
        const routeUrl = new URL('https://xsttgx.example.com/test');
        const originalRequest = new Request('https://example.com/test', {
            method: 'POST',
            headers: new Headers({
                'Content-Type': 'application/text',
                'X-Custom-Header': 'value',
            }),
            body: 'test',
        });

        const result = toRequest(routeUrl, originalRequest.clone());

        expect(result.url).toEqual(routeUrl.toString());
        expect(result.method).toEqual(originalRequest.method);
        expect(result.headers.get('Content-Type')).toEqual(originalRequest.headers.get('Content-Type'));
        expect(result.headers.get('X-Custom-Header')).toEqual(originalRequest.headers.get('X-Custom-Header'));
        expect(result.text()).toEqual(originalRequest.text());
    });

    it('should return dev-spree-aggregator for routing key sr and dev host', () => {
        const routingKey = 'sr';
        const host = 'dev.com';
        const result = getAggregatorEnv(routingKey, host);
        expect(result).toEqual('dev-spree-aggregator');
    });

    it('should return dev-aggregator for routing key pt and dev host', () => {
        const routingKey = 'pt';
        const host = 'dev.com';
        const result = getAggregatorEnv(routingKey, host);
        expect(result).toEqual('dev-aggregator');
    });

    it('should return prod-spree-aggregator for routing key sr and production host', () => {
        const routingKey = 'sr';
        const host = 'prod.com';
        const result = getAggregatorEnv(routingKey, host);
        expect(result).toEqual('prod-spree-aggregator');
    });

    it('should return prod-spree-aggregator for routing key sr and production host', () => {
        const routingKey = 'stage';
        const host = 'dev.com';
        const result = getAggregatorEnv(routingKey, host);
        expect(result).toEqual('stage-aggregator');
    });

    it('should return stage-b2spin.com domain for routing key stage', () => {
          const routingKey: string = 'stage';
          const host: string = 'dev-aggregator.patrianna.com';
          const result = getRouteHost(routingKey, host);
          expect(result).toEqual('https://stage-aggregator.stage-b2spin.com')
      });

    it('should return prod-spree-aggregator for routing key sr and production host', () => {
        const routingKey = 'dev';
        const host = 'dev.com';
        const result = getAggregatorEnv(routingKey, host);
        expect(result).toEqual('dev-aggregator');
    });

    it('should return prod-b2b-pipe for routing key pt and production host', () => {
        const routingKey = 'pt';
        const host = 'prod.com';
        const result = getAggregatorEnv(routingKey, host);
        expect(result).toEqual('prod-b2b-pipe');
    });

    it('should return dev-aggregator for routing key gg and dev host', () => {
        const routingKey = 'ps';
        const host = 'dev.com';
        const result = getAggregatorEnv(routingKey, host);
        expect(result).toEqual('dev-aggregator');
    });

    it('should return prod-aggregator for routing key gg and dev host', () => {
        const routingKey = 'ps';
        const host = 'prod.com';
        const result = getAggregatorEnv(routingKey, host);
        expect(result).toEqual('prod-aggregator');
    });

    function mockRoutingService(): RoutingService {
        return {
            resolveRoutingKey: jest.fn().mockResolvedValue('abcd1234xsttgx')
        } as RoutingService;
    }

    function mockCtx(url: string, headers: {}): Context {
        return {
            req: {
                url: url,
                method: 'GET',
                raw: {
                    url: url,
                    headers: new Headers(headers),
                    body: null,
                    clone: function (): Request {
                        return new Request(url, this);
                    }
                },
                header: function (headerName: string) {
                    return this.raw.headers.get(headerName);
                }
            },
            text: function (text: any) {
                return text;
            }
        } as Context;
    }
});
