import app from '../index';

describe('/robots.txt tests', () => {

    it('should return robots.txt for frontend host', async () => {
        const request = new Request('https://dev-frontend.ysi-dev.com/robots.txt', {
            method: 'GET'
        });

        const response = await app.request(request);

        expect(response.status).toBe(200);
        expect(response.headers.get('Content-Type')).toBe('text/plain');
        expect(response.headers.get('Cache-Control')).toBe('public, max-age=86400');

        const text = await response.text();
        expect(text).toBe('User-agent: *\nDisallow: /');
    });

    it('should return robots.txt for gateway host', async () => {
        const request = new Request('https://dev-gateway.ysi-dev.com/robots.txt', {
            method: 'GET'
        });

        const response = await app.request(request);

        expect(response.status).toBe(200);
        expect(response.headers.get('Content-Type')).toBe('text/plain');
        expect(response.headers.get('Cache-Control')).toBe('public, max-age=86400');

        const text = await response.text();
        expect(text).toBe('User-agent: *\nDisallow: /');
    });

});
