import {filterAnonymousUser} from "./service/filters";
import {handleRequestWithCache, handleRequestWithCacheControl} from "./service/cache";
import { handleRobotsRequest } from "./service/robots";
import {Hono} from "hono";

const frontend: Hono = new Hono();

frontend.get('/v1/get-all-products', filterAnonymousUser, handleRequestWithCache);
frontend.get('/v1/product-layout', filterAnonymousUser, handleRequestWithCache);
frontend.get('/v1/get-product', filterAnonymousUser, handleRequestWithCache);
frontend.get('/robots.txt', handleRobotsRequest);

frontend.post("*", handleRequestWithCacheControl)

export default frontend;