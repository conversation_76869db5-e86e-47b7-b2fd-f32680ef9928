import {Context, Hono} from "hono";
import {routeRequest} from "./service/router";

import stgRouteMatcher from "./service/router/gaming/stg-route-matcher";

const aggregator: Hono = new Hono();
const routable: Hono = new Hono();
const excludedPaths = ["/v1/jackpots", "/v1/games", "/v1/launch", "/v2/freespins"];


const handleRequest = async (ctx, next) => {
    const path = ctx.req.path;
    const isExcluded = excludedPaths.some(excluded => path.includes(excluded));

    if (isExcluded) {
        return next();
    }

    return routeRequest(ctx, stgRouteMatcher);
  };


  ['post', 'delete', 'patch', 'put', 'get'].forEach(method => {
      routable[method]('/*', handleRequest);
  });

aggregator.route('/', routable);

export default aggregator;