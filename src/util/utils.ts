import {ServiceRoute} from "./utils.types";

const allowedQueryParams: string[] = ["brandName", "showAll", "code", "route", "includeInactive"];

export const FRONTEND: ServiceRoute = new ServiceRoute('frontend');
export const GATEWAY: ServiceRoute = new ServiceRoute('gateway');
export const AGGREGATOR: ServiceRoute = new ServiceRoute('aggregator');
export const PAYMENT_GATEWAY: ServiceRoute = new ServiceRoute('payment-gateway');
export const FRAUD_GATEWAY: ServiceRoute = new ServiceRoute('fraud-gateway');

export async function fetchReq(request: Request): Promise<Response> {
    return fetch(request).then(async resp => new Response(await resp.text(), resp));
}

export function getServiceRoute(hostname: string): string {
    for (const service of [FRONTEND, GATEWAY, AGGREGATOR]) {
        if (hostname.includes(service.getName())) {
            return service.getPath();
        }
    }

    return '';
}

export function toNormalizedURL(request: Request): URL {
    const url: URL = new URL(request.url);
    url.search = normalizeQueryParams(url);

    return url;
}

function normalizeQueryParams(url: URL): string {
    const queryParams = new URLSearchParams();

    new URLSearchParams(url.search).forEach((value, key) => {
        if (allowedQueryParams.includes(key)) {
            const isBoolean = key === "showAll" || key === "includeInactive";
            const normalizedValue = isBoolean ? booleanQueryParam(value) : value;

            queryParams.set(key, normalizedValue);
        }
    });

    return queryParams.toString();
}

function booleanQueryParam(value: string): string {
    return value === "true" ? "true" : "false";
}