name = "hono-worker"
main = "src/index.ts"
compatibility_date = "2023-12-18"
send_metrics = false

[[kv_namespaces]]
binding = "KV_payment"
id = "6436c1f772524a2f95574c41ecac1c50"

[observability.logs]
enabled = true

[env.ysi-dev]
account_id = "2a92ddb1f4056b4d40d8a7a76c9ac87c"
name = "frontend-hono-worker-dev"
routes = [
    { pattern = "dev-frontend.ysi-dev.com/v1/dispatch/*", zone_name = "ysi-dev.com" },
    { pattern = "dev-frontend.ysi-dev.com/v1/get-all-products*", zone_name = "ysi-dev.com" },
    { pattern = "dev-frontend.ysi-dev.com/v1/product-layout*", zone_name = "ysi-dev.com" },
    { pattern = "dev-frontend.ysi-dev.com/v1/get-product*", zone_name = "ysi-dev.com" },
    { pattern = "dev-frontend.ysi-dev.com/robots.txt", zone_name = "ysi-dev.com" },
    { pattern = "dev-frontend.lxttx.info/v1/dispatch/*", zone_name = "lxttx.info" },
    { pattern = "dev-frontend.lxttx.info/v1/get-all-products*", zone_name = "lxttx.info" },
    { pattern = "dev-frontend.lxttx.info/v1/product-layout*", zone_name = "lxttx.info" },
    { pattern = "dev-frontend.lxttx.info/v1/get-product*", zone_name = "lxttx.info" },
    { pattern = "dev-frontend.lxttx.info/robots.txt", zone_name = "lxttx.info" },
    { pattern = "dev-frontend.lxttx.net/v1/dispatch/*", zone_name = "lxttx.net" },
    { pattern = "dev-frontend.lxttx.net/v1/get-all-products*", zone_name = "lxttx.net" },
    { pattern = "dev-frontend.lxttx.net/v1/product-layout*", zone_name = "lxttx.net" },
    { pattern = "dev-frontend.lxttx.net/v1/get-product*", zone_name = "lxttx.net" },
    { pattern = "dev-frontend.lxttx.net/robots.txt", zone_name = "lxttx.net" }
]

[env.ysi-prod]
account_id = "2a92ddb1f4056b4d40d8a7a76c9ac87c"
name = "frontend-hono-worker-prod"
routes = [
    { pattern = "prod-frontend.ysi-group.com/v1/get-all-products*", zone_name = "ysi-group.com" },
    { pattern = "prod-frontend.ysi-group.com/v1/product-layout*", zone_name = "ysi-group.com" },
    { pattern = "prod-frontend.ysi-group.com/v1/get-product*", zone_name = "ysi-group.com" },
    { pattern = "prod-frontend.ysi-group.com/robots.txt", zone_name = "ysi-group.com" },
    { pattern = "prod-frontend.pulsz.com/v1/get-all-products*", zone_name = "pulsz.com" },
    { pattern = "prod-frontend.pulsz.com/v1/product-layout*", zone_name = "pulsz.com" },
    { pattern = "prod-frontend.pulsz.com/v1/get-product*", zone_name = "pulsz.com" },
    { pattern = "prod-frontend.pulsz.com/robots.txt", zone_name = "pulsz.com" },
    { pattern = "prod-payment-frontend.pulsz.com/robots.txt", zone_name = "pulsz.com" },
    { pattern = "prod-frontend.pulszbingo.com/v1/get-all-products*", zone_name = "pulszbingo.com" },
    { pattern = "prod-frontend.pulszbingo.com/v1/product-layout*", zone_name = "pulszbingo.com" },
    { pattern = "prod-frontend.pulszbingo.com/v1/get-product*", zone_name = "pulszbingo.com" },
    { pattern = "prod-frontend.pulszbingo.com/robots.txt", zone_name = "pulszbingo.com" }
]

[env.b2-dev]
account_id = "********************************"
name = "frontend-hono-worker-dev"
routes = [
#    cache
    { pattern = "dev-frontend.dev-b2spin.com/v1/dispatch/*", zone_name = "dev-b2spin.com" },
    { pattern = "dev-frontend.dev-b2spin.com/v1/get-all-products*", zone_name = "dev-b2spin.com" },
    { pattern = "dev-frontend.dev-b2spin.com/v1/product-layout*", zone_name = "dev-b2spin.com" },
    { pattern = "dev-frontend.dev-b2spin.com/v1/get-product*", zone_name = "dev-b2spin.com" },
    { pattern = "dev-frontend.mcluck.online/v1/dispatch/*", zone_name = "mcluck.online" },
    { pattern = "dev-frontend.mcluck.online/v1/get-all-products*", zone_name = "mcluck.online" },
    { pattern = "dev-frontend.mcluck.online/v1/product-layout*", zone_name = "mcluck.online" },
    { pattern = "dev-frontend.mcluck.online/v1/get-product*", zone_name = "mcluck.online" },
    { pattern = "dev-frontend.scratchluck.net/v1/dispatch/*", zone_name = "scratchluck.net" },
    { pattern = "dev-frontend.scratchluck.net/v1/get-all-products*", zone_name = "scratchluck.net" },
    { pattern = "dev-frontend.scratchluck.net/v1/product-layout*", zone_name = "scratchluck.net" },
    { pattern = "dev-frontend.scratchluck.net/v1/get-product*", zone_name = "scratchluck.net" },
    { pattern = "dev-frontend.dev-env-hellom.com/v1/dispatch/*", zone_name = "dev-env-hellom.com" },
    { pattern = "dev-frontend.dev-env-hellom.com/v1/get-all-products*", zone_name = "dev-env-hellom.com" },
    { pattern = "dev-frontend.dev-env-hellom.com/v1/product-layout*", zone_name = "dev-env-hellom.com" },
    { pattern = "dev-frontend.dev-env-hellom.com/v1/get-product*", zone_name = "dev-env-hellom.com" },
#    route
    { pattern = "dev-gateway.dev-b2spin.com/v1/pragmatic*", zone_name = "dev-b2spin.com" },
    { pattern = "dev-gateway.dev-b2spin.com/v1/omni*", zone_name = "dev-b2spin.com" },
    { pattern = "dev-payment-gateway.dev-b2spin.com/v1/prizeout*", zone_name = "dev-b2spin.com" },
    { pattern = "dev-payment-gateway.dev-b2spin.com/v1/hook*", zone_name = "dev-b2spin.com" },
    { pattern = "dev-payment-gateway.mcluck.online/v1/prizeout*", zone_name = "mcluck.online" },
    { pattern = "dev-payment-gateway.mcluck.online/v1/hook/*", zone_name = "mcluck.online" },
    { pattern = "dev-payment-gateway.scratchluck.net/v1/prizeout*", zone_name = "scratchluck.net" },
    { pattern = "dev-payment-gateway.scratchluck.net/v1/hook/*", zone_name = "scratchluck.net" },
    { pattern = "dev-payment-gateway.dev-env-hellom.com/v1/prizeout*", zone_name = "dev-env-hellom.com" },
    { pattern = "dev-payment-gateway.dev-env-hellom.com/v1/hook/*", zone_name = "dev-env-hellom.com" },
    { pattern = "dev-fraud-gateway.dev-b2spin.com/v1/hook*", zone_name = "dev-b2spin.com" },
    { pattern = "dev-fraud-gateway.mcluck.online/v1/hook/*", zone_name = "mcluck.online" },
# Robots.txt
    { pattern = "dev-frontend.dev-b2spin.com/robots.txt", zone_name = "dev-b2spin.com" },
    { pattern = "dev-frontend.mcluck.online/robots.txt", zone_name = "mcluck.online" },
    { pattern = "dev-frontend.scratchluck.net/robots.txt", zone_name = "scratchluck.net" },
    { pattern = "dev-frontend.dev-env-hellom.com/robots.txt", zone_name = "dev-env-hellom.com" },
    { pattern = "dev-gateway.dev-b2spin.com/robots.txt", zone_name = "dev-b2spin.com" },
    { pattern = "dev-payment-gateway.dev-b2spin.com/robots.txt", zone_name = "dev-b2spin.com" },
    { pattern = "dev-payment-gateway.mcluck.online/robots.txt", zone_name = "mcluck.online" },
    { pattern = "dev-payment-gateway.scratchluck.net/robots.txt", zone_name = "scratchluck.net" },
    { pattern = "dev-payment-gateway.dev-env-hellom.com/robots.txt", zone_name = "dev-env-hellom.com" },
    { pattern = "dev-fraud-gateway.dev-b2spin.com/robots.txt", zone_name = "dev-b2spin.com" },
    { pattern = "dev-fraud-gateway.mcluck.online/robots.txt", zone_name = "mcluck.online" }
]

[env.b2-prod]
account_id = "********************************"
name = "frontend-hono-worker-prod"
routes = [
# b2spin.com
    { pattern = "prod-frontend.b2spin.com/v1/get-all-products*", zone_name = "b2spin.com" },
    { pattern = "prod-frontend.b2spin.com/v1/product-layout*", zone_name = "b2spin.com" },
    { pattern = "prod-frontend.b2spin.com/v1/get-product*", zone_name = "b2spin.com" },
    { pattern = "prod-frontend.b2spin.com/robots.txt", zone_name = "b2spin.com" },
# mcluck.com
    { pattern = "prod-frontend.mcluck.com/v1/get-all-products*", zone_name = "mcluck.com" },
    { pattern = "prod-frontend.mcluck.com/v1/product-layout*", zone_name = "mcluck.com" },
    { pattern = "prod-frontend.mcluck.com/v1/get-product*", zone_name = "mcluck.com" },
    { pattern = "prod-frontend.mcluck.com/robots.txt", zone_name = "mcluck.com" },
    { pattern = "prod-payment-frontend.mcluck.com/robots.txt", zone_name = "mcluck.com" },
# scratchful.com
    { pattern = "prod-frontend.scratchful.com/v1/get-all-products*", zone_name = "scratchful.com" },
    { pattern = "prod-frontend.scratchful.com/v1/product-layout*", zone_name = "scratchful.com" },
    { pattern = "prod-frontend.scratchful.com/v1/get-product*", zone_name = "scratchful.com" },
    { pattern = "prod-frontend.scratchful.com/robots.txt", zone_name = "scratchful.com" },
    { pattern = "prod-payment-frontend.scratchful.com/robots.txt", zone_name = "scratchful.com" },
# hellomillions.com
    { pattern = "prod-frontend.hellomillions.com/v1/get-all-products*", zone_name = "hellomillions.com" },
    { pattern = "prod-frontend.hellomillions.com/v1/product-layout*", zone_name = "hellomillions.com" },
    { pattern = "prod-frontend.hellomillions.com/v1/get-product*", zone_name = "hellomillions.com" },
    { pattern = "prod-frontend.hellomillions.com/robots.txt", zone_name = "hellomillions.com" },
    { pattern = "prod-payment-frontend.hellomillions.com/robots.txt", zone_name = "hellomillions.com" },
# playfame.com
    { pattern = "prod-frontend.playfame.com/robots.txt", zone_name = "playfame.com" },
    { pattern = "prod-payment-frontend.playfame.com/robots.txt", zone_name = "playfame.com" },
    { pattern = "prod-creator-frontend.playfame.com/robots.txt", zone_name = "playfame.com" },
# spinblitz.com
    { pattern = "prod-frontend.spinblitz.com/robots.txt", zone_name = "spinblitz.com" },
    { pattern = "prod-payment-frontend.spinblitz.com/robots.txt", zone_name = "spinblitz.com" },
    { pattern = "prod-thrill-frontend.spinblitz.com/robots.txt", zone_name = "spinblitz.com" },
# sportsmillions.com
    { pattern = "prod-frontend.sportsmillions.com/robots.txt", zone_name = "sportsmillions.com" },
    { pattern = "prod-randomizer-frontend.sportsmillions.com/robots.txt", zone_name = "sportsmillions.com" },
    { pattern = "prod-payment-frontend.sportsmillions.com/robots.txt", zone_name = "sportsmillions.com" }
]

[env.patrianna-dev]
account_id = "a47a503943ead681243c52e42b70d93a"
name = "aggregator-hono-worker-dev"
routes = [
    #    route
    { pattern = "dev-aggregator.patrianna.com/*", zone_name = "patrianna.com" }
]

[env.jackpota-dev]
account_id = "a5661ec70b3cd5f56547a4a2edd8a0c9"
name = "frontend-hono-worker-dev"
routes = [
    #    route
    { pattern = "frontend.dev-jackpota.com/v1/get-all-products*", zone_name = "dev-jackpota.com" },
    { pattern = "frontend.dev-jackpota.com/v1/product-layout*", zone_name = "dev-jackpota.com" },
    { pattern = "frontend.dev-jackpota.com/v1/get-product*", zone_name = "dev-jackpota.com" },
    { pattern = "frontend.dev-jackpota.com/v1/dispatch/*", zone_name = "dev-jackpota.com" },
    { pattern = "frontend.dev-jackpota.com/robots.txt", zone_name = "dev-jackpota.com" },
    { pattern = "frontend.dev-jackpotalimited.com/v1/get-all-products*", zone_name = "dev-jackpotalimited.com" },
    { pattern = "frontend.dev-jackpotalimited.com/v1/product-layout*", zone_name = "dev-jackpotalimited.com" },
    { pattern = "frontend.dev-jackpotalimited.com/v1/get-product*", zone_name = "dev-jackpotalimited.com" },
    { pattern = "frontend.dev-jackpotalimited.com/v1/dispatch/*", zone_name = "dev-jackpotalimited.com" },
    { pattern = "frontend.dev-jackpotalimited.com/robots.txt", zone_name = "dev-jackpotalimited.com" },
    { pattern = "gateway.dev-jackpotalimited.com/robots.txt", zone_name = "dev-jackpotalimited" }
]

[env.jackpota-prod]
account_id = "a5661ec70b3cd5f56547a4a2edd8a0c9"
name = "frontend-hono-worker-prod"
routes = [
    #    route
    { pattern = "prod-jackpota-frontend.jackpota.com/v1/get-all-products*", zone_name = "jackpota.com" },
    { pattern = "prod-jackpota-frontend.jackpota.com/v1/product-layout*", zone_name = "jackpota.com" },
    { pattern = "prod-jackpota-frontend.jackpota.com/v1/get-product*", zone_name = "jackpota.com" },
    { pattern = "prod-jackpota-frontend.jackpota.com/v1/dispatch/*", zone_name = "jackpota.com" },
    { pattern = "prod-jackpota-frontend.jackpota.com/robots.txt", zone_name = "jackpota.com" },
    { pattern = "prod-jackpota-frontend-int.jackpota.com/v1/get-all-products*", zone_name = "jackpota.com" },
    { pattern = "prod-jackpota-frontend-int.jackpota.com/v1/product-layout*", zone_name = "jackpota.com" },
    { pattern = "prod-jackpota-frontend-int.jackpota.com/v1/get-product*", zone_name = "jackpota.com" },
    { pattern = "prod-jackpota-frontend-int.jackpota.com/v1/dispatch/*", zone_name = "jackpota.com" },
    { pattern = "prod-jackpota-frontend-int.jackpota.com/robots.txt", zone_name = "jackpota.com" }
]

[env.spree-dev]
account_id = "a47a503943ead681243c52e42b70d93a"
name = "frontend-hono-worker-spree-dev"
routes = [
    #    route
    { pattern = "dev-spree-frontend.patrianna.com/v1/get-all-products*", zone_name = "patrianna.com" },
    { pattern = "dev-spree-frontend.patrianna.com/v1/product-layout*", zone_name = "patrianna.com" },
    { pattern = "dev-spree-frontend.patrianna.com/v1/get-product*", zone_name = "patrianna.com" },
    { pattern = "dev-spree-frontend.patrianna.com/v1/dispatch/*", zone_name = "patrianna.com" },
    { pattern = "dev-spree-frontend.patrianna.com/robots.txt", zone_name = "patrianna.com" }
]

[env.spree-prod]
account_id = "a1d7b048c53c69eaf04f719f1a969e21"
name = "frontend-hono-worker-prod"
routes = [
    #    route
    { pattern = "prod-spree-frontend.spree.com/v1/get-all-products*", zone_name = "spree.com" },
    { pattern = "prod-spree-frontend.spree.com/v1/product-layout*", zone_name = "spree.com" },
    { pattern = "prod-spree-frontend.spree.com/v1/get-product*", zone_name = "spree.com" },
    { pattern = "prod-spree-frontend.spree.com/v1/dispatch/*", zone_name = "spree.com" },
    { pattern = "prod-spree-frontend.spree.com/robots.txt", zone_name = "spree.com" }
]

[env.pmsg-prod]
account_id = "098fd3cd170ae4c90712dc649f1ba61b"
name = "frontend-hono-worker-prod"
routes = [
    #    route
    { pattern = "pmsg-prod-frontend.spindoo.us/v1/get-all-products*", zone_name = "spindoo.us" },
    { pattern = "pmsg-prod-frontend.spindoo.us/v1/product-layout*", zone_name = "spindoo.us" },
    { pattern = "pmsg-prod-frontend.spindoo.us/v1/get-product*", zone_name = "spindoo.us" },
    { pattern = "pmsg-prod-frontend.spindoo.us/v1/dispatch/*", zone_name = "spindoo.us" },
    { pattern = "pmsg-prod-frontend.spindoo.us/robots.txt", zone_name = "spindoo.us" }
]

[env.rum_lumi-dev]
account_id = "d902b9c9df2bf8911a4bc49406a0d4bf"
name = "frontend-hono-worker-dev"
routes = [
    #    route
    { pattern = "frontend.mbdevbrand.com/v1/get-all-products*", zone_name = "mbdevbrand.com" },
    { pattern = "frontend.mbdevbrand.com/v1/product-layout*", zone_name = "mbdevbrand.com" },
    { pattern = "frontend.mbdevbrand.com/v1/get-product*", zone_name = "mbdevbrand.com" },
    { pattern = "frontend.mbdevbrand.com/v1/dispatch/*", zone_name = "mbdevbrand.com" },
    { pattern = "frontend.mbdevbrand.com/robots.txt", zone_name = "mbdevbrand.com" },
    { pattern = "frontend.mbdevuber.com/v1/get-all-products*", zone_name = "mbdevuber.com" },
    { pattern = "frontend.mbdevuber.com/v1/product-layout*", zone_name = "mbdevuber.com" },
    { pattern = "frontend.mbdevuber.com/v1/get-product*", zone_name = "mbdevuber.com" },
    { pattern = "frontend.mbdevuber.com/v1/dispatch/*", zone_name = "mbdevuber.com" },
    { pattern = "frontend.mbdevuber.com/robots.txt", zone_name = "mbdevuber.com" },
    { pattern = "gateway.mbdevuber.com/robots.txt", zone_name = "mbdevuber.com" }
]

[env.rum_lumi-prod]
account_id = "d902b9c9df2bf8911a4bc49406a0d4bf"
name = "frontend-hono-worker-prod"
routes = [
    #    route
    { pattern = "rum-prod-frontend.megabonanza.com/v1/get-all-products*", zone_name = "megabonanza.com" },
    { pattern = "rum-prod-frontend.megabonanza.com/v1/product-layout*", zone_name = "megabonanza.com" },
    { pattern = "rum-prod-frontend.megabonanza.com/v1/get-product*", zone_name = "megabonanza.com" },
    { pattern = "rum-prod-frontend.megabonanza.com/v1/dispatch/*", zone_name = "megabonanza.com" },
    { pattern = "rum-prod-frontend.megabonanza.com/robots.txt", zone_name = "megabonanza.com" },
    { pattern = "rum-prod-frontend-int.megabonanza.com/v1/get-all-products*", zone_name = "megabonanza.com" },
    { pattern = "rum-prod-frontend-int.megabonanza.com/v1/product-layout*", zone_name = "megabonanza.com" },
    { pattern = "rum-prod-frontend-int.megabonanza.com/v1/get-product*", zone_name = "megabonanza.com" },
    { pattern = "rum-prod-frontend-int.megabonanza.com/v1/dispatch/*", zone_name = "megabonanza.com" },
    { pattern = "rum-prod-frontend-int.megabonanza.com/robots.txt", zone_name = "megabonanza.com" }
]

[env.fullstop-dev]
account_id = "8a64e7d215226fb120a2f4bdc525ce54"
name = "frontend-hono-worker-dev"
routes = [
    #    route
    { pattern = "frontend.dev-ace.io/v1/get-all-products*", zone_name = "dev-ace.io" },
    { pattern = "frontend.dev-ace.io/v1/product-layout*", zone_name = "dev-ace.io" },
    { pattern = "frontend.dev-ace.io/v1/get-product*", zone_name = "dev-ace.io" },
    { pattern = "frontend.dev-ace.io/robots.txt", zone_name = "dev-ace.io" },
    { pattern = "frontend.dev-fullstoplimited.com/v1/get-all-products*", zone_name = "dev-fullstoplimited.com" },
    { pattern = "frontend.dev-fullstoplimited.com/v1/product-layout*", zone_name = "dev-fullstoplimited.com" },
    { pattern = "frontend.dev-fullstoplimited.com/v1/get-product*", zone_name = "dev-fullstoplimited.com" },
    { pattern = "frontend.dev-fullstoplimited.com/robots.txt", zone_name = "dev-fullstoplimited.com" },
    { pattern = "gateway.dev-ace.io/robots.txt", zone_name = "dev-ace.io" },
    { pattern = "gateway.dev-fullstoplimited.com/robots.txt", zone_name = "dev-fullstoplimited.com" }
]

[env.fullstop-prod]
account_id = "8a64e7d215226fb120a2f4bdc525ce54"
name = "frontend-hono-worker-prod"
routes = [
    #    route
    { pattern = "frontend.fullstoplimited.com/v1/get-all-products*", zone_name = "fullstoplimited.com" },
    { pattern = "frontend.fullstoplimited.com/v1/product-layout*", zone_name = "fullstoplimited.com" },
    { pattern = "frontend.fullstoplimited.com/v1/get-product*", zone_name = "fullstoplimited.com" },
    { pattern = "frontend.fullstoplimited.com/robots.txt", zone_name = "fullstoplimited.com" },
    { pattern = "frontend.ace.com/v1/get-all-products*", zone_name = "ace.com" },
    { pattern = "frontend.ace.com/v1/product-layout*", zone_name = "ace.com" },
    { pattern = "frontend.ace.com/v1/get-product*", zone_name = "ace.com" },
    { pattern = "frontend.ace.com/robots.txt", zone_name = "ace.com" }
]
