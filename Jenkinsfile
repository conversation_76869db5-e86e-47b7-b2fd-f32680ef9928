pipeline {
    agent {
        node {
            label 'patrianna-dev'
        }
    }

    // Настроить автоматический запуск только для ветки dev
    triggers {
        pollSCM('H/2 * * * *')
    }

    tools {
        nodejs '21.6.1'
    }

    stages {
        stage('build') {
            when {
                anyOf {
                    branch 'dev' // Автоматический запуск только для ветки dev
                    triggeredBy 'UserIdCause' // Также разрешить ручной запуск
                }
            }
            steps {
                sh 'npm install'
                sh 'npm run test'
                sh 'npm run build'
            }
        }
        stage('deploy') {
            when {
                anyOf {
                    branch 'dev' // Автоматический запуск только для ветки dev
                    triggeredBy 'UserIdCause' // Также разрешить ручной запуск
                }
            }
            steps {
                withFolderProperties {
                    script {
                        env.TARGET_ENV = env.BRANCH_NAME == 'main' ? 'prod' : 'dev'

                        def DEPLOY_STACK_NAME = "${env.JOB_NAME}".split('/').minus("${env.BRANCH_NAME}").last()

                        def credentialId = "CLOUDFLARE_API_TOKEN_${DEPLOY_STACK_NAME.toUpperCase()}"

                        echo "Deploying with DEPLOY_STACK_NAME: ${DEPLOY_STACK_NAME}"

                        withCredentials([string(credentialsId: credentialId, variable: 'CLOUDFLARE_TOKEN')]) {
                            deployToCloudflare(DEPLOY_STACK_NAME)
                        }
                    }
                }
            }
        }
    }
}

def deployToCloudflare(envName) {
    echo "Deploying to ${envName}-${env.TARGET_ENV} with token"
    withEnv(["CLOUDFLARE_API_TOKEN=${env.CLOUDFLARE_TOKEN}"]) {
        sh "wrangler deploy --env ${envName}-${env.TARGET_ENV}"
    }
}