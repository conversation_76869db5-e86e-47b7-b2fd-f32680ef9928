{"scripts": {"dev": "run-p dev:*", "dev:wrangler": "wrangler pages dev dist --live-reload", "dev:esbuild": "esbuild --bundle src/index.ts --format=esm --watch --outfile=dist/_worker.js", "build": "esbuild --bundle src/index.ts --format=esm --outfile=dist/_worker.js", "deploy": "wrangler pages publish dist", "test": "jest --verbose"}, "dependencies": {"hono": "^3.12.8", "npm-run-all": "^4.1.5", "run-p": "^0.0.0"}, "devDependencies": {"@cloudflare/workers-types": "^4.20240129.0", "@types/jest": "^29.5.11", "@types/jest-when": "^3.5.5", "@types/tldjs": "^2.3.4", "jest": "^29.7.0", "jest-environment-miniflare": "^2.14.2", "tldjs": "^2.3.1", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "undici-types": "^6.6.0", "wrangler": "^4.31.0"}}